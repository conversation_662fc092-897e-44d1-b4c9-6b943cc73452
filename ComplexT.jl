"""
MODULE JULIA AUTONOME - ComplexT
================================

MÉTRIQUE DE COMPLEXITÉ - ComplexT (PRIORITÉ ÉLEVÉE)
FORMULE NOUVELLE : Complexité de Lempel-Ziv pour Détection de Patterns
Mesure la complexité algorithmique et détecte les patterns répétitifs exploitables.

FORMULE : ComplexT_n = LZ_complexity(INDEX3_sequence[1:n]) / n
Normalisation par n pour obtenir une mesure relative indépendante de la longueur.

USAGE :
    using ComplexT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE", ...]
    result = calculer_complexite_lempel_ziv(formulas, sequence, n)

OBJECTIF : Détecter les patterns répétitifs et mesurer la complexité algorithmique
"""

module ComplexT

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════

"""
Extrait INDEX3 (résultat) depuis une valeur INDEX5
"""
function extraire_index3(index5_value::String)::String
    parts = split(index5_value, "_")
    return length(parts) >= 3 ? parts[3] : "BANKER"
end

"""
Convertit une séquence INDEX3 en chaîne binaire pour l'algorithme LZ
BANKER → 00, PLAYER → 01, TIE → 10
"""
function index3_vers_binaire(index3_sequence::Vector{String})::String
    mapping = Dict(
        "BANKER" => "00",
        "PLAYER" => "01", 
        "TIE" => "10"
    )
    
    binaire = ""
    for idx3 in index3_sequence
        binaire *= get(mapping, idx3, "11")  # "11" pour valeurs inconnues
    end
    
    return binaire
end

# ═══════════════════════════════════════════════════════════════════
# ALGORITHME DE LEMPEL-ZIV (LZ77)
# ═══════════════════════════════════════════════════════════════════

"""
Implémentation de l'algorithme de complexité de Lempel-Ziv (LZ77)
Compte le nombre de sous-chaînes distinctes nécessaires pour reconstruire la séquence.
"""
function lempel_ziv_complexity(sequence::String)::Int
    if isempty(sequence)
        return 0
    end
    
    n = length(sequence)
    complexity = 0
    i = 1
    
    while i <= n
        # Chercher la plus longue sous-chaîne qui apparaît déjà avant la position i
        max_length = 0
        
        # Parcourir toutes les positions précédentes
        for j in 1:(i-1)
            # Chercher la plus longue correspondance à partir de la position j
            length_match = 0
            
            while (i + length_match <= n) && 
                  (j + length_match <= i - 1) && 
                  (sequence[i + length_match] == sequence[j + length_match])
                length_match += 1
            end
            
            max_length = max(max_length, length_match)
        end
        
        # Si aucune correspondance trouvée, ajouter le caractère actuel comme nouveau pattern
        if max_length == 0
            complexity += 1
            i += 1
        else
            # Ajouter le pattern trouvé + le caractère suivant (s'il existe)
            complexity += 1
            i += max_length + (i + max_length <= n ? 1 : 0)
        end
    end
    
    return complexity
end

"""
Version optimisée de Lempel-Ziv avec fenêtre glissante
Plus efficace pour les longues séquences.
"""
function lempel_ziv_complexity_optimized(sequence::String, window_size::Int=1000)::Int
    if isempty(sequence)
        return 0
    end
    
    n = length(sequence)
    complexity = 0
    i = 1
    
    while i <= n
        # Limiter la recherche à une fenêtre pour l'efficacité
        start_search = max(1, i - window_size)
        max_length = 0
        
        # Chercher dans la fenêtre
        for j in start_search:(i-1)
            length_match = 0
            
            while (i + length_match <= n) && 
                  (j + length_match <= i - 1) && 
                  (sequence[i + length_match] == sequence[j + length_match])
                length_match += 1
            end
            
            max_length = max(max_length, length_match)
        end
        
        if max_length == 0
            complexity += 1
            i += 1
        else
            complexity += 1
            i += max_length + (i + max_length <= n ? 1 : 0)
        end
    end
    
    return complexity
end

# ═══════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - Complexité de Lempel-Ziv
# ═══════════════════════════════════════════════════════════════════

"""
    calculer_complexite_lempel_ziv(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int,
        normalize::Bool=true
    ) where T -> T

Calcule la complexité de Lempel-Ziv de la séquence INDEX3[1:n].

FORMULE : ComplexT_n = LZ_complexity(INDEX3_sequence[1:n]) / n (si normalize=true)
         ComplexT_n = LZ_complexity(INDEX3_sequence[1:n]) (si normalize=false)

INTERPRÉTATION :
- ComplexT faible : Séquence très structurée, patterns répétitifs détectables
- ComplexT élevé : Séquence complexe, peu de patterns répétitifs
- ComplexT ≈ 0.5 : Séquence typique avec structure modérée
- ComplexT ≈ 1.0 : Séquence très complexe, proche du hasard

USAGE PRÉDICTIF :
- ComplexT décroissant : Le système devient plus prévisible
- ComplexT croissant : Le système devient moins prévisible  
- Changements brusques : Possibles changements de régime
- Plateaux : Régimes stables avec complexité constante

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques
- sequence : Séquence complète INDEX5
- n : Longueur de la sous-séquence à analyser [1:n]
- normalize : Si true, normalise par n (défaut: true)

RETOUR :
- Complexité de Lempel-Ziv (normalisée ou absolue)
"""
function calculer_complexite_lempel_ziv(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    normalize::Bool=true
) where T<:AbstractFloat
    
    if n <= 0 || n > length(sequence)
        return zero(T)
    end
    
    # Extraire la séquence INDEX3
    index3_sequence = String[]
    for i in 1:n
        idx3 = extraire_index3(sequence[i])
        push!(index3_sequence, idx3)
    end
    
    if isempty(index3_sequence)
        return zero(T)
    end
    
    # Convertir en représentation binaire pour l'algorithme LZ
    sequence_binaire = index3_vers_binaire(index3_sequence)
    
    # Calculer la complexité LZ
    # Utiliser la version optimisée pour les longues séquences
    lz_complexity = if length(sequence_binaire) > 2000
        lempel_ziv_complexity_optimized(sequence_binaire, 1000)
    else
        lempel_ziv_complexity(sequence_binaire)
    end
    
    # Normaliser si demandé
    if normalize && n > 0
        return T(lz_complexity) / T(n)
    else
        return T(lz_complexity)
    end
end

"""
    analyser_evolution_complexite(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int,
        window_size::Int=100
    ) where T -> Vector{T}

Analyse l'évolution de la complexité sur une fenêtre glissante.
Utile pour détecter les changements de régime.

RETOUR :
- Vecteur des complexités pour chaque position de la fenêtre
"""
function analyser_evolution_complexite(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    window_size::Int=100
) where T<:AbstractFloat
    
    if n <= window_size || n > length(sequence)
        return T[]
    end
    
    complexites = T[]
    
    # Calculer la complexité pour chaque fenêtre
    for i in window_size:n
        start_pos = i - window_size + 1
        
        # Extraire la fenêtre
        window_sequence = sequence[start_pos:i]
        
        # Calculer la complexité de cette fenêtre
        complexity = calculer_complexite_lempel_ziv(formulas, window_sequence, window_size, true)
        push!(complexites, complexity)
    end
    
    return complexites
end

"""
    detecter_patterns_repetitifs(
        sequence::Vector{String}, 
        n::Int,
        min_pattern_length::Int=2,
        min_occurrences::Int=3
    ) -> Dict{String, Int}

Détecte les patterns répétitifs dans la séquence INDEX3.

RETOUR :
- Dictionnaire : pattern → nombre d'occurrences
"""
function detecter_patterns_repetitifs(
    sequence::Vector{String},
    n::Int,
    min_pattern_length::Int=2,
    min_occurrences::Int=3
)::Dict{String, Int}
    
    if n <= 0 || n > length(sequence)
        return Dict{String, Int}()
    end
    
    # Extraire la séquence INDEX3
    index3_sequence = String[]
    for i in 1:n
        idx3 = extraire_index3(sequence[i])
        push!(index3_sequence, idx3)
    end
    
    patterns = Dict{String, Int}()
    
    # Chercher tous les patterns de longueur min_pattern_length à n/2
    max_pattern_length = min(10, div(n, 2))  # Limiter pour l'efficacité
    
    for pattern_length in min_pattern_length:max_pattern_length
        for start_pos in 1:(n - pattern_length + 1)
            # Extraire le pattern
            pattern = join(index3_sequence[start_pos:(start_pos + pattern_length - 1)], "→")
            
            # Compter les occurrences
            patterns[pattern] = get(patterns, pattern, 0) + 1
        end
    end
    
    # Filtrer par nombre minimum d'occurrences
    filtered_patterns = Dict{String, Int}()
    for (pattern, count) in patterns
        if count >= min_occurrences
            filtered_patterns[pattern] = count
        end
    end
    
    return filtered_patterns
end

# ═══════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════

export calculer_complexite_lempel_ziv, analyser_evolution_complexite, detecter_patterns_repetitifs

end # module ComplexT
