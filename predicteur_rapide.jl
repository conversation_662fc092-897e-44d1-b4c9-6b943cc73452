"""
PRÉDICTEUR RAPIDE - VERSION OPTIMISÉE POUR GROS DATASETS
========================================================

Version optimisée du prédicteur pour traiter rapidement de gros fichiers JSON
avec affichage du progrès en temps réel et traitement par blocs.

Auteur: Maître de l'Entropie (toujours éveillé !)
Date: 2025-07-14
"""

using JSON
using Printf

println("🎓 PRÉDICTEUR RAPIDE - Maître de l'Entropie (Version Optimisée)")
println("=" ^ 70)

# Inclusion de notre système optimisé
include("Analyseur.jl")
using .PredicteurIndex5

# ═══════════════════════════════════════════════════════════════════
# CONFIGURATION OPTIMISÉE
# ═══════════════════════════════════════════════════════════════════

const DATASET_PATH = "C:\\Users\\<USER>\\Desktop\\E\\partie\\dataset.json"
const MIN_HISTORIQUE = 15
const BLOC_SIZE = 1000  # Traiter par blocs de 1000 mains
const AFFICHAGE_FREQ = 100  # Afficher le progrès toutes les 100 prédictions

# ═══════════════════════════════════════════════════════════════════
# EXTRACTION RAPIDE
# ═══════════════════════════════════════════════════════════════════

"""
Extraction rapide et optimisée de la séquence INDEX5
"""
function extraction_rapide(filepath::String)
    println("⚡ EXTRACTION RAPIDE EN COURS...")
    
    # Lecture streaming du JSON
    data = JSON.parsefile(filepath)
    
    sequence = String[]
    total_parties = 0
    
    if haskey(data, "parties_condensees")
        println("📊 Traitement des parties condensées...")
        
        for (i, partie) in enumerate(data["parties_condensees"])
            total_parties += 1
            
            if haskey(partie, "mains_condensees")
                for main in partie["mains_condensees"]
                    if haskey(main, "index5")
                        push!(sequence, main["index5"])
                    end
                end
            end
            
            # Affichage du progrès
            if i % 100 == 0
                println("   Parties traitées: $i ($(length(sequence)) mains)")
            end
        end
    end
    
    println("✅ Extraction terminée:")
    println("   Total parties: $total_parties")
    println("   Total mains: $(length(sequence))")
    println("   Premiers: $(sequence[1:min(5, length(sequence))])")
    
    return sequence
end

# ═══════════════════════════════════════════════════════════════════
# PRÉDICTION RAPIDE PAR BLOCS
# ═══════════════════════════════════════════════════════════════════

"""
Structure simplifiée pour les résultats
"""
mutable struct StatsRapides
    total::Int
    correct::Int
    correct_banker::Int
    correct_player::Int
    correct_tie::Int
    pred_banker::Int
    pred_player::Int
    pred_tie::Int
    confiance_totale::Float64
    confiance_max::Float64
    meilleure_serie::Int
    serie_actuelle::Int
end

StatsRapides() = StatsRapides(0, 0, 0, 0, 0, 0, 0, 0, 0.0, 0.0, 0, 0)

"""
Prédiction rapide par blocs
"""
function prediction_rapide(sequence::Vector{String})
    println("\n⚡ PRÉDICTIONS RAPIDES EN COURS...")
    println("=" ^ 50)
    
    formulas = PredicteurIndex5.FormulasTheoretical{Float64}()
    stats = StatsRapides()
    
    total_predictions = length(sequence) - MIN_HISTORIQUE - 1
    println("📊 Total prédictions à effectuer: $total_predictions")
    
    # Barre de progression
    debut_temps = time()
    
    for i in MIN_HISTORIQUE:(length(sequence)-1)
        # Prédiction
        prediction_data = PredicteurIndex5.predire_index3_suivant(formulas, sequence, i)
        
        # Résultat réel
        parts = split(sequence[i+1], "_")
        index3_reel = length(parts) >= 3 ? parts[3] : "BANKER"
        
        # Vérification
        correct = prediction_data.prediction == index3_reel
        
        # Mise à jour des stats
        stats.total += 1
        if correct
            stats.correct += 1
            stats.serie_actuelle += 1
            stats.meilleure_serie = max(stats.meilleure_serie, stats.serie_actuelle)
        else
            stats.serie_actuelle = 0
        end
        
        # Stats par type
        if prediction_data.prediction == "BANKER"
            stats.pred_banker += 1
            if correct
                stats.correct_banker += 1
            end
        elseif prediction_data.prediction == "PLAYER"
            stats.pred_player += 1
            if correct
                stats.correct_player += 1
            end
        else
            stats.pred_tie += 1
            if correct
                stats.correct_tie += 1
            end
        end
        
        # Confiance
        stats.confiance_totale += prediction_data.confiance
        stats.confiance_max = max(stats.confiance_max, prediction_data.confiance)
        
        # Affichage du progrès
        if stats.total % AFFICHAGE_FREQ == 0
            taux_actuel = stats.correct / stats.total
            temps_ecoule = time() - debut_temps
            vitesse = stats.total / temps_ecoule
            temps_restant = (total_predictions - stats.total) / vitesse
            
            println("📈 Main $(i+1): $(round(taux_actuel*100, digits=1))% ($(stats.correct)/$(stats.total)) | " *
                   "Vitesse: $(round(vitesse, digits=1)) pred/s | " *
                   "Restant: $(round(temps_restant, digits=0))s")
        end
        
        # Signaler les prédictions à haute confiance
        if prediction_data.confiance > 0.4
            status = correct ? "✅" : "❌"
            println("🎯 Main $(i+1): Confiance $(round(prediction_data.confiance*100, digits=1))% → $(prediction_data.prediction) $status")
        end
    end
    
    return stats
end

# ═══════════════════════════════════════════════════════════════════
# RAPPORT RAPIDE
# ═══════════════════════════════════════════════════════════════════

"""
Génère un rapport rapide et concis
"""
function rapport_rapide(stats::StatsRapides)
    println("\n🎯 RAPPORT FINAL - PERFORMANCE GLOBALE")
    println("=" ^ 60)
    
    taux_global = stats.correct / stats.total
    confiance_moyenne = stats.confiance_totale / stats.total
    
    println("📊 RÉSULTATS PRINCIPAUX:")
    println("   🎯 Taux de réussite global: $(round(taux_global*100, digits=2))%")
    println("   📈 Prédictions correctes: $(stats.correct) / $(stats.total)")
    println("   🔥 Meilleure série: $(stats.meilleure_serie) prédictions consécutives")
    println("   💪 Confiance moyenne: $(round(confiance_moyenne*100, digits=2))%")
    println("   🚀 Confiance maximale: $(round(stats.confiance_max*100, digits=2))%")
    
    println("\n📈 PERFORMANCE PAR TYPE:")
    if stats.pred_banker > 0
        taux_banker = stats.correct_banker / stats.pred_banker
        println("   🏦 BANKER: $(stats.correct_banker)/$(stats.pred_banker) = $(round(taux_banker*100, digits=1))%")
    end
    if stats.pred_player > 0
        taux_player = stats.correct_player / stats.pred_player
        println("   👤 PLAYER: $(stats.correct_player)/$(stats.pred_player) = $(round(taux_player*100, digits=1))%")
    end
    if stats.pred_tie > 0
        taux_tie = stats.correct_tie / stats.pred_tie
        println("   🤝 TIE: $(stats.correct_tie)/$(stats.pred_tie) = $(round(taux_tie*100, digits=1))%")
    end
    
    # Évaluation de la performance
    println("\n🎓 ÉVALUATION DU MAÎTRE:")
    if taux_global > 0.55
        println("   🏆 EXCELLENT! Votre système dépasse significativement le hasard!")
    elseif taux_global > 0.50
        println("   ✅ BON! Votre système montre un avantage détectable!")
    elseif taux_global > 0.45
        println("   ⚠️  MOYEN. Le système fonctionne mais peut être amélioré.")
    else
        println("   ❌ FAIBLE. Révision nécessaire des paramètres.")
    end
    
    if confiance_moyenne > 0.2
        println("   💡 Confiance élevée: Le système est sûr de ses prédictions!")
    end
    
    if stats.meilleure_serie > 10
        println("   🔥 Excellente capacité de détection des patterns!")
    end
end

# ═══════════════════════════════════════════════════════════════════
# PROGRAMME PRINCIPAL OPTIMISÉ
# ═══════════════════════════════════════════════════════════════════

"""
Programme principal optimisé pour la vitesse
"""
function main_rapide()
    try
        println("🚀 DÉMARRAGE DU PRÉDICTEUR RAPIDE...")
        debut_total = time()
        
        # Vérification du fichier
        if !isfile(DATASET_PATH)
            error("❌ Fichier non trouvé: $DATASET_PATH")
        end
        
        file_size_mb = filesize(DATASET_PATH) / (1024 * 1024)
        println("📁 Fichier: $(round(file_size_mb, digits=1)) MB")
        
        # Extraction rapide
        sequence = extraction_rapide(DATASET_PATH)
        
        if length(sequence) < MIN_HISTORIQUE + 10
            error("❌ Séquence trop courte: $(length(sequence)) mains")
        end
        
        # Prédictions rapides
        stats = prediction_rapide(sequence)
        
        # Rapport final
        rapport_rapide(stats)
        
        temps_total = time() - debut_total
        vitesse_globale = stats.total / temps_total
        
        println("\n⚡ TRAITEMENT TERMINÉ!")
        println("   ⏱️  Temps total: $(round(temps_total, digits=1)) secondes")
        println("   🚀 Vitesse moyenne: $(round(vitesse_globale, digits=1)) prédictions/seconde")
        println("   📊 Données traitées: $(length(sequence)) mains")
        
        println("\n🎓 ANALYSE TERMINÉE AVEC SUCCÈS!")
        println("=" ^ 50)
        
    catch e
        println("❌ ERREUR: $e")
        println("\nStacktrace:")
        for (exc, bt) in Base.catch_stack()
            showerror(stdout, exc, bt)
            println()
        end
    end
end

# Lancement du programme
if abspath(PROGRAM_FILE) == @__FILE__
    main_rapide()
end
