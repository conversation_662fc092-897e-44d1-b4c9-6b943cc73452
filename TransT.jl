"""
MODULE JULIA AUTONOME - TransT
==============================

MÉTRIQUE PRÉDICTIVE DIRECTE - TransT (PRIORITÉ MAXIMALE)
FORMULE NOUVELLE : Entropie de Transition pour Prédiction Directe INDEX3
Mesure directement la prédictibilité du prochain résultat INDEX3.

FORMULE : TransT_n = H(INDEX3_{n+1} | INDEX3_n, INDEX1_n, INDEX2_n, Contexte_k)
Où :
- INDEX3_{n+1} = résultat à prédire (BANKER/PLAYER/TIE)
- INDEX3_n = résultat actuel
- INDEX1_n, INDEX2_n = état actuel (SYNC/DESYNC, nombre cartes)
- Contexte_k = patterns des k dernières transitions

USAGE :
    using TransT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE", ...]
    result = calculer_entropie_transition_predictive(formulas, sequence, n, k=3)

OBJECTIF : Mesure directe de la prédictibilité du prochain INDEX3
"""

module TransT

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES POUR EXTRACTION D'INDEX
# ═══════════════════════════════════════════════════════════════════

"""
Extrait INDEX1 (SYNC/DESYNC) depuis une valeur INDEX5
"""
function extraire_index1(index5_value::String)::String
    parts = split(index5_value, "_")
    return length(parts) >= 1 ? parts[1] : "0"
end

"""
Extrait INDEX2 (nombre de cartes) depuis une valeur INDEX5
"""
function extraire_index2(index5_value::String)::String
    parts = split(index5_value, "_")
    return length(parts) >= 2 ? parts[2] : "A"
end

"""
Extrait INDEX3 (résultat) depuis une valeur INDEX5
"""
function extraire_index3(index5_value::String)::String
    parts = split(index5_value, "_")
    return length(parts) >= 3 ? parts[3] : "BANKER"
end

# ═══════════════════════════════════════════════════════════════════
# CALCULS D'ENTROPIE POUR TRANSITIONS
# ═══════════════════════════════════════════════════════════════════

"""
Calcule l'entropie d'une distribution empirique
"""
function calculer_entropie_empirique(
    formulas::FormulasTheoretical{T},
    counts::Dict{String, Int},
    total::Int
) where T<:AbstractFloat
    if total <= 0
        return zero(T)
    end
    
    entropy = zero(T)
    for (value, count) in counts
        if count > 0
            p = T(count) / T(total)
            entropy -= p * (log(p) / log(formulas.base))
        end
    end
    return entropy
end

"""
Crée une signature de contexte pour les k dernières transitions
"""
function creer_signature_contexte(
    sequence::Vector{String},
    position::Int,
    k::Int
)::String
    if position <= k
        return "DEBUT"
    end
    
    # Extraire les k dernières transitions INDEX3
    transitions = String[]
    for i in (position-k):(position-1)
        idx3 = extraire_index3(sequence[i])
        push!(transitions, idx3)
    end
    
    return join(transitions, "→")
end

# ═══════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - Entropie de Transition Prédictive
# ═══════════════════════════════════════════════════════════════════

"""
    calculer_entropie_transition_predictive(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int, 
        k::Int=3
    ) where T -> T

Calcule l'entropie de transition pour prédire INDEX3_{n+1} basée sur l'état complet actuel.

FORMULE : TransT_n = H(INDEX3_{n+1} | INDEX3_n, INDEX1_n, INDEX2_n, Contexte_k)

Cette métrique mesure directement la prédictibilité du prochain résultat en tenant compte :
1. Du résultat actuel (INDEX3_n)
2. De l'état SYNC/DESYNC actuel (INDEX1_n)  
3. Du nombre de cartes actuel (INDEX2_n)
4. Du pattern des k dernières transitions

INTERPRÉTATION PRÉDICTIVE :
- TransT = 0 : Prochain INDEX3 parfaitement prévisible (déterministe)
- TransT faible : Système très prévisible, bonnes chances de prédiction
- TransT élevé : Système imprévisible, prédiction difficile
- TransT = log₂(3) ≈ 1.585 : Complètement aléatoire (3 résultats équiprobables)

AVANTAGES :
- Mesure DIRECTE de la prédictibilité
- Intègre tous les facteurs pertinents
- Optimisé pour la prédiction du prochain coup
- Base idéale pour algorithmes de prédiction

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques
- sequence : Séquence complète INDEX5
- n : Position actuelle (on veut prédire INDEX3_{n+1})
- k : Taille du contexte de transitions (défaut: 3)

RETOUR :
- Entropie de transition en bits (≥ 0, ≤ log₂(3))
"""
function calculer_entropie_transition_predictive(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    k::Int=3
) where T<:AbstractFloat
    
    # Vérifications de base
    if n <= k || n >= length(sequence)
        return zero(T)
    end
    
    # Construire les échantillons d'entraînement
    # Chaque échantillon : (état_complet) → INDEX3_futur
    etats_complets = String[]
    index3_futurs = String[]
    
    # Parcourir la séquence pour construire les échantillons
    for i in (k+1):(n-1)  # On s'arrête à n-1 car on veut INDEX3_{i+1}
        # État actuel complet
        idx1_actuel = extraire_index1(sequence[i])
        idx2_actuel = extraire_index2(sequence[i])
        idx3_actuel = extraire_index3(sequence[i])
        
        # Contexte des k dernières transitions
        contexte = creer_signature_contexte(sequence, i, k)
        
        # Créer la signature d'état complet
        etat_complet = "$(idx3_actuel)|$(idx1_actuel)|$(idx2_actuel)|$(contexte)"
        
        # Résultat futur à prédire
        idx3_futur = extraire_index3(sequence[i+1])
        
        push!(etats_complets, etat_complet)
        push!(index3_futurs, idx3_futur)
    end
    
    if isempty(index3_futurs)
        return zero(T)
    end
    
    # Grouper par état complet et calculer l'entropie conditionnelle
    # H(INDEX3_{n+1} | État_Complet) = ∑ P(état) × H(INDEX3 | état)
    
    etat_groups = Dict{String, Vector{String}}()
    for (i, etat) in enumerate(etats_complets)
        if !haskey(etat_groups, etat)
            etat_groups[etat] = String[]
        end
        push!(etat_groups[etat], index3_futurs[i])
    end
    
    # Calculer l'entropie conditionnelle pondérée
    entropie_transition = zero(T)
    total_samples = length(index3_futurs)
    
    for (etat, idx3_list) in etat_groups
        # Poids de cet état dans l'échantillon
        weight = T(length(idx3_list)) / T(total_samples)
        
        # Compter les résultats INDEX3 pour cet état
        counts = Dict{String, Int}()
        for idx3 in idx3_list
            counts[idx3] = get(counts, idx3, 0) + 1
        end
        
        # Calculer H(INDEX3 | cet_état_spécifique)
        h_local = calculer_entropie_empirique(formulas, counts, length(idx3_list))
        
        # Ajouter la contribution pondérée
        entropie_transition += weight * h_local
    end
    
    return entropie_transition
end

"""
    calculer_matrice_transition_index3(
        sequence::Vector{String},
        n::Int
    ) -> Dict{String, Dict{String, Float64}}

Calcule la matrice de transition empirique pour INDEX3.
Utile pour analyser les patterns de transition.

RETOUR :
- Dictionnaire : état_actuel → {résultat_futur → probabilité}
"""
function calculer_matrice_transition_index3(
    sequence::Vector{String},
    n::Int
)::Dict{String, Dict{String, Float64}}
    
    matrice = Dict{String, Dict{String, Float64}}()
    
    # Compter les transitions
    transitions = Dict{String, Dict{String, Int}}()
    
    for i in 1:(n-1)
        if i < length(sequence)
            etat_actuel = extraire_index3(sequence[i])
            etat_futur = extraire_index3(sequence[i+1])
            
            if !haskey(transitions, etat_actuel)
                transitions[etat_actuel] = Dict{String, Int}()
            end
            
            transitions[etat_actuel][etat_futur] = get(transitions[etat_actuel], etat_futur, 0) + 1
        end
    end
    
    # Convertir en probabilités
    for (etat_actuel, counts) in transitions
        total = sum(values(counts))
        matrice[etat_actuel] = Dict{String, Float64}()
        
        for (etat_futur, count) in counts
            matrice[etat_actuel][etat_futur] = Float64(count) / Float64(total)
        end
    end
    
    return matrice
end

# ═══════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════

export calculer_entropie_transition_predictive, calculer_matrice_transition_index3

end # module TransT
