"""
MODULE JULIA AUTONOME - AutocorrT
=================================

MÉTRIQUE DE MÉMOIRE TEMPORELLE - AutocorrT (PRIORITÉ ÉLEVÉE)
FORMULE NOUVELLE : Autocorrélation Entropique pour Mémoire du Système
Mesure la mémoire temporelle et les dépendances à long terme dans INDEX3.

FORMULE : AutocorrT_n(lag) = I(INDEX3_i ; INDEX3_{i+lag}) pour i ∈ [1, n-lag]
Information mutuelle entre INDEX3 à la position i et INDEX3 à la position i+lag.

USAGE :
    using AutocorrT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE", ...]
    result = calculer_autocorrelation_entropique(formulas, sequence, n, lag=5)

OBJECTIF : Quantifier la mémoire temporelle et les cycles dans INDEX3
"""

module AutocorrT

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES
# ═══════════════════════════════════════════════════════════════════

"""
Extrait INDEX3 (résultat) depuis une valeur INDEX5
"""
function extraire_index3(index5_value::String)::String
    parts = split(index5_value, "_")
    return length(parts) >= 3 ? parts[3] : "BANKER"
end

"""
Calcule l'entropie d'une distribution empirique
"""
function calculer_entropie_empirique(
    formulas::FormulasTheoretical{T},
    counts::Dict{String, Int},
    total::Int
) where T<:AbstractFloat
    if total <= 0
        return zero(T)
    end
    
    entropy = zero(T)
    for (value, count) in counts
        if count > 0
            p = T(count) / T(total)
            entropy -= p * (log(p) / log(formulas.base))
        end
    end
    return entropy
end

"""
Calcule l'entropie jointe de deux variables
"""
function calculer_entropie_jointe(
    formulas::FormulasTheoretical{T},
    pairs::Vector{Tuple{String, String}}
) where T<:AbstractFloat
    if isempty(pairs)
        return zero(T)
    end
    
    # Compter les paires
    counts = Dict{Tuple{String, String}, Int}()
    for pair in pairs
        counts[pair] = get(counts, pair, 0) + 1
    end
    
    total = length(pairs)
    entropy = zero(T)
    
    for (pair, count) in counts
        if count > 0
            p = T(count) / T(total)
            entropy -= p * (log(p) / log(formulas.base))
        end
    end
    
    return entropy
end

# ═══════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - Autocorrélation Entropique
# ═══════════════════════════════════════════════════════════════════

"""
    calculer_autocorrelation_entropique(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int,
        lag::Int=1
    ) where T -> T

Calcule l'autocorrélation entropique de INDEX3 avec un décalage donné.

FORMULE : AutocorrT_n(lag) = I(INDEX3_i ; INDEX3_{i+lag})
        = H(INDEX3_i) + H(INDEX3_{i+lag}) - H(INDEX3_i, INDEX3_{i+lag})

INTERPRÉTATION :
- AutocorrT = 0 : Aucune dépendance temporelle au lag donné
- AutocorrT > 0 : Dépendance temporelle détectée
- Plus AutocorrT est élevé, plus la mémoire temporelle est forte

USAGE PRÉDICTIF :
- lag=1 : Dépendance immédiate (coup suivant)
- lag=2,3,4... : Mémoire à moyen terme
- lag=10,20,30... : Cycles et mémoire à long terme
- Pics d'autocorrélation : Périodes caractéristiques du système

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques
- sequence : Séquence complète INDEX5
- n : Longueur de la sous-séquence à analyser [1:n]
- lag : Décalage temporel à analyser

RETOUR :
- Information mutuelle (autocorrélation entropique) en bits (≥ 0)
"""
function calculer_autocorrelation_entropique(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    lag::Int=1
) where T<:AbstractFloat
    
    if n <= lag || n > length(sequence) || lag <= 0
        return zero(T)
    end
    
    # Extraire les séquences INDEX3
    index3_sequence = String[]
    for i in 1:n
        idx3 = extraire_index3(sequence[i])
        push!(index3_sequence, idx3)
    end
    
    if length(index3_sequence) <= lag
        return zero(T)
    end
    
    # Construire les paires (INDEX3_i, INDEX3_{i+lag})
    pairs = Tuple{String, String}[]
    values_i = String[]
    values_i_plus_lag = String[]
    
    for i in 1:(length(index3_sequence) - lag)
        value_i = index3_sequence[i]
        value_i_plus_lag = index3_sequence[i + lag]
        
        push!(pairs, (value_i, value_i_plus_lag))
        push!(values_i, value_i)
        push!(values_i_plus_lag, value_i_plus_lag)
    end
    
    if isempty(pairs)
        return zero(T)
    end
    
    # CALCUL 1: H(INDEX3_i)
    counts_i = Dict{String, Int}()
    for value in values_i
        counts_i[value] = get(counts_i, value, 0) + 1
    end
    h_i = calculer_entropie_empirique(formulas, counts_i, length(values_i))
    
    # CALCUL 2: H(INDEX3_{i+lag})
    counts_i_plus_lag = Dict{String, Int}()
    for value in values_i_plus_lag
        counts_i_plus_lag[value] = get(counts_i_plus_lag, value, 0) + 1
    end
    h_i_plus_lag = calculer_entropie_empirique(formulas, counts_i_plus_lag, length(values_i_plus_lag))
    
    # CALCUL 3: H(INDEX3_i, INDEX3_{i+lag})
    h_joint = calculer_entropie_jointe(formulas, pairs)
    
    # CALCUL FINAL: Information Mutuelle
    # I(X;Y) = H(X) + H(Y) - H(X,Y)
    info_mutuelle = h_i + h_i_plus_lag - h_joint
    
    # S'assurer que le résultat est non-négatif
    return max(zero(T), info_mutuelle)
end

"""
    calculer_spectre_autocorrelation(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int,
        max_lag::Int=20
    ) where T -> Vector{T}

Calcule le spectre d'autocorrélation pour différents lags.
Utile pour détecter les périodicités et cycles.

RETOUR :
- Vecteur des autocorrélations pour lag = 1, 2, ..., max_lag
"""
function calculer_spectre_autocorrelation(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    max_lag::Int=20
) where T<:AbstractFloat
    
    if n <= max_lag || n > length(sequence)
        return T[]
    end
    
    autocorrelations = T[]
    
    for lag in 1:max_lag
        autocorr = calculer_autocorrelation_entropique(formulas, sequence, n, lag)
        push!(autocorrelations, autocorr)
    end
    
    return autocorrelations
end

"""
    detecter_periodicites(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int,
        max_lag::Int=50,
        seuil::T=0.01
    ) where T -> Vector{Int}

Détecte les périodicités significatives dans INDEX3.

RETOUR :
- Vecteur des lags où l'autocorrélation dépasse le seuil
"""
function detecter_periodicites(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    max_lag::Int=50,
    seuil::T=T(0.01)
) where T<:AbstractFloat
    
    periodicites = Int[]
    
    if n <= max_lag || n > length(sequence)
        return periodicites
    end
    
    for lag in 1:max_lag
        autocorr = calculer_autocorrelation_entropique(formulas, sequence, n, lag)
        
        if autocorr > seuil
            push!(periodicites, lag)
        end
    end
    
    return periodicites
end

"""
    calculer_memoire_effective(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int,
        seuil_decay::T=0.01
    ) where T -> Int

Calcule la mémoire effective du système (lag où l'autocorrélation devient négligeable).

RETOUR :
- Lag de mémoire effective (nombre de coups de "mémoire" du système)
"""
function calculer_memoire_effective(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    seuil_decay::T=T(0.01)
) where T<:AbstractFloat
    
    if n <= 10 || n > length(sequence)
        return 0
    end
    
    max_lag = min(50, div(n, 4))  # Limiter la recherche
    
    for lag in 1:max_lag
        autocorr = calculer_autocorrelation_entropique(formulas, sequence, n, lag)
        
        if autocorr <= seuil_decay
            return lag - 1  # Retourner le dernier lag significatif
        end
    end
    
    return max_lag  # Si toujours significatif, retourner la limite
end

"""
    analyser_tendance_autocorrelation(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int,
        lag::Int=1,
        window_size::Int=100
    ) where T -> Vector{T}

Analyse l'évolution de l'autocorrélation sur une fenêtre glissante.
Utile pour détecter les changements de régime dans la mémoire temporelle.

RETOUR :
- Vecteur des autocorrélations pour chaque position de la fenêtre
"""
function analyser_tendance_autocorrelation(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    lag::Int=1,
    window_size::Int=100
) where T<:AbstractFloat
    
    if n <= window_size + lag || n > length(sequence)
        return T[]
    end
    
    autocorrelations = T[]
    
    for i in (window_size + lag):n
        start_pos = i - window_size + 1
        window_sequence = sequence[start_pos:i]
        
        autocorr = calculer_autocorrelation_entropique(formulas, window_sequence, window_size, lag)
        push!(autocorrelations, autocorr)
    end
    
    return autocorrelations
end

# ═══════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════

export calculer_autocorrelation_entropique, calculer_spectre_autocorrelation, 
       detecter_periodicites, calculer_memoire_effective, analyser_tendance_autocorrelation

end # module AutocorrT
