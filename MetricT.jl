"""
MODULE JULIA AUTONOME - MetricT
===============================

MÉTRIQUE SIMPLIFIÉE - MetricT (PRIORITÉ 3)
FORMULE SIMPLIFIÉE : Entropie Marginale Instantanée
Mesure l'entropie du n-ème élément conditionné par l'historique (sans pondération artificielle).

FORMULE : MetricT_n = H_theo(X_n | X₁,...,X_{n-1})
Entropie conditionnelle pure du n-ème élément - mesure directe de sa prédictibilité.

USAGE :
    using MetricT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE"]
    result = calculer_entropie_marginale_instantanee(formulas, sequence, 3)

DÉPENDANCES : AUCUNE - Module complètement autonome et simplifié
"""

module MetricT

# ═══════════════════════════════════════════════════════════════════════════════
# STRUCTURE CENTRALISÉE
# ═══════════════════════════════════════════════════════════════════════════════

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION DÉPENDANCE - ShannonT (INCLUSE POUR AUTONOMIE)
# ═══════════════════════════════════════════════════════════════════════════════

"""
Fonction ShannonT incluse pour rendre le module MetricT autonome.
Calcule l'entropie de Shannon théorique pour une séquence croissante [main 1 : main n].
"""
function calculer_formule1B_shannon_jointe_theo(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    # Extraire la sous-séquence jusqu'à la main n
    subsequence = sequence[1:n]

    # Compter les occurrences observées dans la séquence
    counts = Dict{String, Int}()
    for value in subsequence
        counts[value] = get(counts, value, 0) + 1
    end

    # Calculer l'entropie de Shannon théorique basée sur les probabilités INDEX5
    entropy = zero(T)

    # Pour chaque valeur unique observée, utiliser sa probabilité théorique INDEX5
    for (value, count) in counts
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        if p_theo > zero(T)
            # Entropie de Shannon pure : H(X) = -∑ p_theo(x) log₂(p_theo(x))
            entropy -= p_theo * (log(p_theo) / log(formulas.base))
        end
    end

    return entropy
end

# ═══════════════════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE SIMPLIFIÉE - MetricT
# ═══════════════════════════════════════════════════════════════════════════════

"""
    calculer_entropie_marginale_instantanee(formulas::FormulasTheoretical{T}, sequence::Vector{String}, n::Int) where T -> T

Calcule l'entropie marginale instantanée du n-ème élément conditionné par l'historique.
Version simplifiée sans pondération artificielle - mesure directe de la prédictibilité.

FORMULE : MetricT_n = H_theo(X_n | X₁,...,X_{n-1})

CALCUL :
- Si n = 1 : H_theo(X₁) = -log₂(p_theo(x₁)) (pas de conditionnement)
- Si n > 1 : H_theo(X_n | X₁,...,X_{n-1}) = H_theo(X₁,...,X_n) - H_theo(X₁,...,X_{n-1})

INTERPRÉTATION :
- MetricT faible : Le n-ème élément est très prévisible étant donné l'historique
- MetricT élevé : Le n-ème élément apporte beaucoup d'information nouvelle
- MetricT = 0 : Le n-ème élément est parfaitement prévisible (déterministe)

USAGE EN ANALYSE PRÉDICTIVE :
- Mesure directe de la prédictibilité de chaque élément
- Détection des éléments surprenants (MetricT élevé)
- Identification des patterns prévisibles (MetricT faible)
- Base pour évaluer la qualité des prédictions

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques INDEX5
- sequence : Séquence de valeurs INDEX5 (ex: ["0_A_BANKER", "1_B_PLAYER", ...])
- n : Position de l'élément à analyser [1:n]

RETOUR :
- Entropie conditionnelle du n-ème élément en bits (si base=2.0)
"""
function calculer_entropie_marginale_instantanee(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int
) where T<:AbstractFloat
    if n <= 0 || n > length(sequence)
        return zero(T)
    end

    if n == 1
        # H_theo(X₁) - pas de conditionnement
        value = sequence[1]
        p_theo = get(formulas.theoretical_probs, value, formulas.epsilon)
        return p_theo > zero(T) ? -(log(p_theo) / log(formulas.base)) : zero(T)
    else
        # H_theo(X_n | X₁,...,X_{n-1}) = H_theo(X₁,...,X_n) - H_theo(X₁,...,X_{n-1})
        h_n = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n)
        h_n_minus_1 = calculer_formule1B_shannon_jointe_theo(formulas, sequence, n-1)
        return h_n - h_n_minus_1
    end
end

# ═══════════════════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════════════════

export calculer_entropie_marginale_instantanee

end # module MetricT
