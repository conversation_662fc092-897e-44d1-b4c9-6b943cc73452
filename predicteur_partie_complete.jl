"""
PRÉDICTEUR DE PARTIE COMPLÈTE - APPLICATION DU SYSTÈME OPTIMISÉ
===============================================================

Programme d'application utilisant notre système de prédiction optimisé
pour analyser et prédire une partie complète de baccarat depuis dataset.json.

Utilise tous nos modules optimisés :
- InfoMutT.jl (Information mutuelle prédictive) 🥇
- TransT.jl (Entropie de transition directe) 🥇  
- ComplexT.jl (Complexité de Lempel-Ziv) 🥈
- AutocorrT.jl (Autocorrélation entropique) 🥈
- PredictT.jl (Système de prédiction intégré) 🏆

Auteur: Maître de l'Entropie
Date: 2025-07-14
"""

using JSON
using Printf

println("🎓 PRÉDICTEUR DE PARTIE COMPLÈTE - Maître de l'Entropie")
println("=" ^ 70)

# Inclusion de notre système optimisé
include("Analyseur.jl")
using .PredicteurIndex5

# ═══════════════════════════════════════════════════════════════════
# CONFIGURATION
# ═══════════════════════════════════════════════════════════════════

const DATASET_PATH = "C:\\Users\\<USER>\\Desktop\\E\\partie\\dataset.json"
const MIN_HISTORIQUE = 15  # Minimum de mains pour commencer les prédictions
const SEUIL_CONFIANCE_ELEVEE = 0.3  # Seuil pour signaler les prédictions à haute confiance

# ═══════════════════════════════════════════════════════════════════
# STRUCTURES DE DONNÉES
# ═══════════════════════════════════════════════════════════════════

"""
Structure pour stocker les résultats d'une prédiction
"""
struct ResultatPrediction
    main_numero::Int
    index3_reel::String
    prediction::String
    banker_prob::Float64
    player_prob::Float64
    tie_prob::Float64
    confiance::Float64
    correct::Bool
    metriques::PredicteurIndex5.MetriquesOptimisees{Float64}
end

"""
Structure pour les statistiques globales
"""
mutable struct StatistiquesGlobales
    total_predictions::Int
    predictions_correctes::Int
    predictions_banker::Int
    predictions_player::Int
    predictions_tie::Int
    correct_banker::Int
    correct_player::Int
    correct_tie::Int
    confiance_moyenne::Float64
    confiance_max::Float64
    meilleure_serie::Int
    serie_actuelle::Int
end

# ═══════════════════════════════════════════════════════════════════
# FONCTIONS DE CHARGEMENT ET PARSING
# ═══════════════════════════════════════════════════════════════════

"""
Charge et parse le dataset JSON
"""
function charger_dataset(filepath::String)
    println("📥 Chargement du dataset: $filepath")
    
    if !isfile(filepath)
        error("❌ Fichier non trouvé: $filepath")
    end
    
    file_size_mb = filesize(filepath) / (1024 * 1024)
    println("📊 Taille du fichier: $(round(file_size_mb, digits=1)) MB")
    
    println("⏳ Parsing JSON en cours...")
    data = JSON.parsefile(filepath)
    
    println("✅ Dataset chargé avec succès")
    return data
end

"""
Extrait la séquence INDEX5 depuis les données JSON
"""
function extraire_sequence_index5(data)
    println("🔍 Extraction de la séquence INDEX5...")
    
    # Adapter selon la structure réelle du JSON
    # Supposons que les données sont dans data["parties"] ou data["mains"]
    sequence = String[]
    
    if haskey(data, "parties_condensees")
        # Format avec parties condensées
        for partie in data["parties_condensees"]
            if haskey(partie, "index5_sequence")
                append!(sequence, partie["index5_sequence"])
            end
        end
    elseif haskey(data, "mains")
        # Format avec mains directes
        for main in data["mains"]
            if haskey(main, "index5")
                push!(sequence, main["index5"])
            end
        end
    elseif haskey(data, "sequence")
        # Format avec séquence directe
        sequence = data["sequence"]
    else
        # Essayer de détecter automatiquement la structure
        println("🔍 Détection automatique de la structure...")
        keys_data = collect(keys(data))
        println("   Clés disponibles: $keys_data")
        
        # Prendre la première clé qui semble contenir des données
        for key in keys_data
            if isa(data[key], Vector) && !isempty(data[key])
                println("   Utilisation de la clé: $key")
                if isa(data[key][1], String)
                    sequence = data[key]
                    break
                elseif isa(data[key][1], Dict)
                    # Essayer d'extraire depuis les dictionnaires
                    for item in data[key]
                        if haskey(item, "index5")
                            push!(sequence, item["index5"])
                        end
                    end
                    break
                end
            end
        end
    end
    
    if isempty(sequence)
        error("❌ Impossible d'extraire la séquence INDEX5 du dataset")
    end
    
    println("✅ Séquence extraite: $(length(sequence)) mains")
    println("   Premiers éléments: $(sequence[1:min(5, length(sequence))])")
    
    return sequence
end

# ═══════════════════════════════════════════════════════════════════
# FONCTIONS DE PRÉDICTION ET ANALYSE
# ═══════════════════════════════════════════════════════════════════

"""
Effectue les prédictions sur toute la séquence
"""
function predire_sequence_complete(sequence::Vector{String})
    println("\n🚀 DÉBUT DES PRÉDICTIONS")
    println("=" ^ 50)
    
    # Initialisation
    formulas = PredicteurIndex5.FormulasTheoretical{Float64}()
    resultats = ResultatPrediction[]
    stats = StatistiquesGlobales(0, 0, 0, 0, 0, 0, 0, 0, 0.0, 0.0, 0, 0)
    
    # Prédictions séquentielles
    for i in MIN_HISTORIQUE:(length(sequence)-1)
        # Prédire INDEX3 pour la main i+1
        prediction_data = PredicteurIndex5.predire_index3_suivant(formulas, sequence, i)
        
        # Extraire le résultat réel
        _, _, index3_reel = PredicteurIndex5.extraire_indices(sequence[i+1])
        
        # Vérifier si la prédiction est correcte
        correct = prediction_data.prediction == index3_reel
        
        # Créer le résultat
        resultat = ResultatPrediction(
            i+1,
            index3_reel,
            prediction_data.prediction,
            prediction_data.banker_prob,
            prediction_data.player_prob,
            prediction_data.tie_prob,
            prediction_data.confiance,
            correct,
            prediction_data.metriques
        )
        
        push!(resultats, resultat)
        
        # Mettre à jour les statistiques
        mettre_a_jour_statistiques!(stats, resultat)
        
        # Affichage périodique
        if i % 100 == 0
            taux_actuel = stats.predictions_correctes / stats.total_predictions
            println("📊 Main $i: Taux de réussite = $(round(taux_actuel*100, digits=1))% ($(stats.predictions_correctes)/$(stats.total_predictions))")
        end
        
        # Signaler les prédictions à haute confiance
        if prediction_data.confiance > SEUIL_CONFIANCE_ELEVEE
            status = correct ? "✅" : "❌"
            println("🎯 Main $(i+1): Confiance élevée $(round(prediction_data.confiance*100, digits=1))% → $(prediction_data.prediction) $status")
        end
    end
    
    return resultats, stats
end

"""
Met à jour les statistiques globales
"""
function mettre_a_jour_statistiques!(stats::StatistiquesGlobales, resultat::ResultatPrediction)
    stats.total_predictions += 1
    
    if resultat.correct
        stats.predictions_correctes += 1
        stats.serie_actuelle += 1
        stats.meilleure_serie = max(stats.meilleure_serie, stats.serie_actuelle)
    else
        stats.serie_actuelle = 0
    end
    
    # Compter par type de prédiction
    if resultat.prediction == "BANKER"
        stats.predictions_banker += 1
        if resultat.correct
            stats.correct_banker += 1
        end
    elseif resultat.prediction == "PLAYER"
        stats.predictions_player += 1
        if resultat.correct
            stats.correct_player += 1
        end
    else  # TIE
        stats.predictions_tie += 1
        if resultat.correct
            stats.correct_tie += 1
        end
    end
    
    # Mettre à jour la confiance
    stats.confiance_moyenne = (stats.confiance_moyenne * (stats.total_predictions - 1) + resultat.confiance) / stats.total_predictions
    stats.confiance_max = max(stats.confiance_max, resultat.confiance)
end

# ═══════════════════════════════════════════════════════════════════
# FONCTIONS D'ANALYSE ET RAPPORT
# ═══════════════════════════════════════════════════════════════════

"""
Génère un rapport détaillé des résultats
"""
function generer_rapport(resultats::Vector{ResultatPrediction}, stats::StatistiquesGlobales)
    println("\n📊 RAPPORT DÉTAILLÉ DES PRÉDICTIONS")
    println("=" ^ 60)
    
    # Statistiques générales
    taux_global = stats.predictions_correctes / stats.total_predictions
    println("🎯 PERFORMANCE GLOBALE:")
    println("   Total prédictions: $(stats.total_predictions)")
    println("   Prédictions correctes: $(stats.predictions_correctes)")
    println("   Taux de réussite: $(round(taux_global*100, digits=2))%")
    println("   Confiance moyenne: $(round(stats.confiance_moyenne*100, digits=2))%")
    println("   Confiance maximale: $(round(stats.confiance_max*100, digits=2))%")
    println("   Meilleure série: $(stats.meilleure_serie) prédictions consécutives")
    
    # Performance par type
    println("\n📈 PERFORMANCE PAR TYPE:")
    if stats.predictions_banker > 0
        taux_banker = stats.correct_banker / stats.predictions_banker
        println("   BANKER: $(stats.correct_banker)/$(stats.predictions_banker) = $(round(taux_banker*100, digits=1))%")
    end
    if stats.predictions_player > 0
        taux_player = stats.correct_player / stats.predictions_player
        println("   PLAYER: $(stats.correct_player)/$(stats.predictions_player) = $(round(taux_player*100, digits=1))%")
    end
    if stats.predictions_tie > 0
        taux_tie = stats.correct_tie / stats.predictions_tie
        println("   TIE: $(stats.correct_tie)/$(stats.predictions_tie) = $(round(taux_tie*100, digits=1))%")
    end
    
    # Analyse des métriques
    println("\n🔬 ANALYSE DES MÉTRIQUES:")
    analyser_metriques(resultats)
    
    # Périodes de haute performance
    println("\n🎯 PÉRIODES DE HAUTE PERFORMANCE:")
    analyser_periodes_performance(resultats)
end

"""
Analyse les métriques entropiques
"""
function analyser_metriques(resultats::Vector{ResultatPrediction})
    if isempty(resultats)
        return
    end
    
    # Moyennes des métriques
    cond_moy = mean([r.metriques.cond_t for r in resultats])
    divkl_moy = mean([r.metriques.divkl_t for r in resultats])
    infomut_moy = mean([r.metriques.infomut_t for r in resultats])
    trans_moy = mean([r.metriques.trans_t for r in resultats])
    complex_moy = mean([r.metriques.complex_t for r in resultats])
    autocorr_moy = mean([r.metriques.autocorr_t for r in resultats])
    
    println("   CondT moyen: $(round(cond_moy, digits=4)) bits")
    println("   DivKLT moyen: $(round(divkl_moy, digits=4)) bits")
    println("   InfoMutT moyen: $(round(infomut_moy, digits=4)) bits")
    println("   TransT moyen: $(round(trans_moy, digits=4)) bits")
    println("   ComplexT moyen: $(round(complex_moy, digits=4))")
    println("   AutocorrT moyen: $(round(autocorr_moy, digits=4)) bits")
end

"""
Analyse les périodes de haute performance
"""
function analyser_periodes_performance(resultats::Vector{ResultatPrediction})
    # Trouver les périodes où le taux de réussite est > 60% sur 50 prédictions
    window_size = 50
    seuil_performance = 0.6
    
    periodes_excellentes = []
    
    for i in 1:(length(resultats) - window_size + 1)
        window = resultats[i:(i + window_size - 1)]
        taux = sum([r.correct for r in window]) / window_size
        
        if taux > seuil_performance
            push!(periodes_excellentes, (i, i + window_size - 1, taux))
        end
    end
    
    if !isempty(periodes_excellentes)
        println("   Périodes excellentes (>$(round(seuil_performance*100))% sur $window_size mains):")
        for (debut, fin, taux) in periodes_excellentes[1:min(5, length(periodes_excellentes))]
            println("     Mains $debut-$fin: $(round(taux*100, digits=1))%")
        end
    else
        println("   Aucune période de haute performance détectée")
    end
end

# ═══════════════════════════════════════════════════════════════════
# PROGRAMME PRINCIPAL
# ═══════════════════════════════════════════════════════════════════

"""
Programme principal
"""
function main()
    try
        # Chargement des données
        data = charger_dataset(DATASET_PATH)
        sequence = extraire_sequence_index5(data)
        
        if length(sequence) < MIN_HISTORIQUE + 10
            error("❌ Séquence trop courte ($(length(sequence)) mains). Minimum requis: $(MIN_HISTORIQUE + 10)")
        end
        
        # Prédictions
        resultats, stats = predire_sequence_complete(sequence)
        
        # Rapport
        generer_rapport(resultats, stats)
        
        println("\n🎓 ANALYSE TERMINÉE AVEC SUCCÈS!")
        println("=" ^ 50)
        
    catch e
        println("❌ ERREUR: $e")
        println("Stacktrace:")
        for (exc, bt) in Base.catch_stack()
            showerror(stdout, exc, bt)
            println()
        end
    end
end

# Fonction utilitaire pour la moyenne
mean(x) = sum(x) / length(x)

# Lancement du programme
if abspath(PROGRAM_FILE) == @__FILE__
    main()
end
