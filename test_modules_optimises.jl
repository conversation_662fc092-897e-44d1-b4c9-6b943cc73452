"""
TEST DES MODULES OPTIMISÉS POUR PRÉDICTION INDEX3
=================================================

Script de test pour vérifier le bon fonctionnement des nouveaux modules
optimisés pour la prédiction de INDEX3 dans le baccarat.

Auteur: Maître de l'Entropie
Date: 2025-07-14
"""

println("🎓 TEST DES MODULES OPTIMISÉS - Maître de l'Entropie")
println("=" ^ 60)

# Inclusion du module principal
include("Analyseur.jl")
using .PredicteurIndex5

# Données de test (séquence INDEX5 simulée)
sequence_test = [
    "0_A_BANKER", "1_B_PLAYER", "0_C_TIE", "1_A_BANKER", "0_B_PLAYER",
    "1_C_TIE", "0_A_BANKER", "1_B_BANKER", "0_A_PLAYER", "1_C_BANKER",
    "0_B_TIE", "1_A_PLAYER", "0_C_BANKER", "1_B_TIE", "0_A_BANKER",
    "1_A_PLAYER", "0_B_BANKER", "1_C_PLAYER", "0_A_TIE", "1_B_BANKER"
]

println("📊 Séquence de test: $(length(sequence_test)) éléments")
println("Premiers éléments: $(sequence_test[1:5])")
println()

# Initialisation des formulas
println("🔧 Initialisation des FormulasTheoretical...")
formulas = PredicteurIndex5.FormulasTheoretical{Float64}()
println("✅ FormulasTheoretical initialisé avec base=$(formulas.base), epsilon=$(formulas.epsilon)")
println()

# Test des modules conservés
println("✅ TEST DES MODULES CONSERVÉS")
println("-" ^ 40)

try
    # Test CondT
    println("🧪 Test CondT.jl...")
    cond_result = PredicteurIndex5.calculer_formule5B_conditionnelle_theo(formulas, sequence_test, 10)
    println("   CondT(10) = $(round(cond_result, digits=6)) bits")

    # Test DivKLT
    println("🧪 Test DivKLT.jl...")
    divkl_result = PredicteurIndex5.calculer_formule6B_divergence_kl_theo(formulas, sequence_test, 10)
    println("   DivKLT(10) = $(round(divkl_result, digits=6)) bits")

    # Test MetricT simplifié
    println("🧪 Test MetricT.jl (simplifié)...")
    metric_result = PredicteurIndex5.calculer_entropie_marginale_instantanee(formulas, sequence_test, 10)
    println("   MetricT(10) = $(round(metric_result, digits=6)) bits")

    println("✅ Modules conservés: SUCCÈS")

catch e
    println("❌ Erreur dans les modules conservés: $e")
end

println()

# Test des nouveaux modules
println("🚀 TEST DES NOUVEAUX MODULES")
println("-" ^ 40)

try
    # Test InfoMutT
    println("🧪 Test InfoMutT.jl...")
    infomut_result = PredicteurIndex5.calculer_information_mutuelle_predictive(formulas, sequence_test, 15, 5)
    println("   InfoMutT(15, k=5) = $(round(infomut_result, digits=6)) bits")

    # Test TransT
    println("🧪 Test TransT.jl...")
    trans_result = PredicteurIndex5.calculer_entropie_transition_predictive(formulas, sequence_test, 15, 3)
    println("   TransT(15, k=3) = $(round(trans_result, digits=6)) bits")

    # Test ComplexT
    println("🧪 Test ComplexT.jl...")
    complex_result = PredicteurIndex5.calculer_complexite_lempel_ziv(formulas, sequence_test, 15, true)
    println("   ComplexT(15) = $(round(complex_result, digits=6)) (normalisé)")

    # Test AutocorrT
    println("🧪 Test AutocorrT.jl...")
    autocorr_result = PredicteurIndex5.calculer_autocorrelation_entropique(formulas, sequence_test, 15, 1)
    println("   AutocorrT(15, lag=1) = $(round(autocorr_result, digits=6)) bits")

    println("✅ Nouveaux modules: SUCCÈS")

catch e
    println("❌ Erreur dans les nouveaux modules: $e")
end

println()

# Test du système de prédiction intégré
println("🏆 TEST DU SYSTÈME DE PRÉDICTION INTÉGRÉ")
println("-" ^ 50)

try
    println("🧪 Test PredictT.jl...")

    # Test de prédiction
    prediction = PredicteurIndex5.predire_index3_suivant(formulas, sequence_test, 15)

    println("   Prédiction INDEX3: $(prediction.prediction)")
    println("   Probabilités:")
    println("     - BANKER: $(round(prediction.banker_prob, digits=4))")
    println("     - PLAYER: $(round(prediction.player_prob, digits=4))")
    println("     - TIE: $(round(prediction.tie_prob, digits=4))")
    println("   Confiance: $(round(prediction.confiance, digits=4))")

    # Test d'évaluation
    println("\n🧪 Test d'évaluation de qualité...")
    evaluation = PredicteurIndex5.evaluer_qualite_prediction(formulas, sequence_test, 10, 18)

    if !isempty(evaluation)
        println("   Taux de réussite: $(round(evaluation["taux_reussite"], digits=4))")
        println("   Confiance moyenne: $(round(evaluation["confiance_moyenne"], digits=4))")
        println("   Total prédictions: $(Int(evaluation["total_predictions"]))")
    else
        println("   Pas assez de données pour l'évaluation")
    end

    println("✅ Système de prédiction: SUCCÈS")

catch e
    println("❌ Erreur dans le système de prédiction: $e")
end

println()

# Test de la nouvelle structure MetriquesOptimisees
println("📊 TEST DE LA STRUCTURE OPTIMISÉE")
println("-" ^ 40)

try
    println("🧪 Test MetriquesOptimisees...")

    # Calculer toutes les métriques
    cond_val = PredicteurIndex5.calculer_formule5B_conditionnelle_theo(formulas, sequence_test, 12)
    divkl_val = PredicteurIndex5.calculer_formule6B_divergence_kl_theo(formulas, sequence_test, 12)
    metric_val = PredicteurIndex5.calculer_entropie_marginale_instantanee(formulas, sequence_test, 12)
    infomut_val = PredicteurIndex5.calculer_information_mutuelle_predictive(formulas, sequence_test, 12, 3)
    trans_val = PredicteurIndex5.calculer_entropie_transition_predictive(formulas, sequence_test, 12, 2)
    complex_val = PredicteurIndex5.calculer_complexite_lempel_ziv(formulas, sequence_test, 12, true)
    autocorr_val = PredicteurIndex5.calculer_autocorrelation_entropique(formulas, sequence_test, 12, 1)

    # Créer la structure
    metriques = PredicteurIndex5.MetriquesOptimisees{Float64}(
        cond_val, divkl_val, metric_val, infomut_val,
        trans_val, complex_val, autocorr_val
    )

    println("   Structure créée avec succès:")
    println("     - CondT: $(round(metriques.cond_t, digits=4))")
    println("     - DivKLT: $(round(metriques.divkl_t, digits=4))")
    println("     - MetricT: $(round(metriques.metric_t, digits=4))")
    println("     - InfoMutT: $(round(metriques.infomut_t, digits=4))")
    println("     - TransT: $(round(metriques.trans_t, digits=4))")
    println("     - ComplexT: $(round(metriques.complex_t, digits=4))")
    println("     - AutocorrT: $(round(metriques.autocorr_t, digits=4))")

    println("✅ Structure optimisée: SUCCÈS")

catch e
    println("❌ Erreur dans la structure optimisée: $e")
end

println()

# Résumé final
println("🎯 RÉSUMÉ FINAL")
println("=" ^ 30)
println("✅ Modules supprimés: TauxT, CrossT, TopoT, BlockT, ShannonT")
println("⚠️  Module modifié: MetricT (simplifié)")
println("🚀 Nouveaux modules: InfoMutT, TransT, ComplexT, AutocorrT, PredictT")
println("📊 Structure optimisée: MetriquesOptimisees (7 métriques)")
println()
println("🎓 OPTIMISATION TERMINÉE - Système prêt pour la prédiction INDEX3!")
println("=" ^ 60)
