"""
MODULE JULIA AUTONOME - InfoMutT
================================

MÉTRIQUE PRÉDICTIVE CRUCIALE - InfoMutT (PRIORITÉ MAXIMALE)
FORMULE NOUVELLE : Information Mutuelle Conditionnelle pour Prédiction INDEX3
Mesure la dépendance entre INDEX1/INDEX2 et INDEX3 futur conditionné par l'historique.

FORMULE : InfoMutT_n = I(INDEX3_{n+1} ; INDEX1_n, INDEX2_n | Historique_{n-k:n})
Où :
- INDEX3_{n+1} = résultat à prédire (BANKER/PLAYER/TIE)
- INDEX1_n, INDEX2_n = état actuel (SYNC/DESYNC, nombre cartes)
- Historique_{n-k:n} = contexte des k dernières mains

USAGE :
    using InfoMutT
    formulas = FormulasTheoretical{Float64}()
    sequence = ["0_A_BANKER", "1_B_PLAYER", "0_C_TIE", ...]
    result = calculer_information_mutuelle_predictive(formulas, sequence, n, k=5)

OBJECTIF : Quantifier la capacité prédictive de INDEX1/INDEX2 pour INDEX3
"""

module InfoMutT

# Import de la structure FormulasTheoretical depuis le module parent
import ..FormulasTheoretical

# ═══════════════════════════════════════════════════════════════════
# FONCTIONS UTILITAIRES POUR EXTRACTION D'INDEX
# ═══════════════════════════════════════════════════════════════════

"""
Extrait INDEX1 (SYNC/DESYNC) depuis une valeur INDEX5
"""
function extraire_index1(index5_value::String)::String
    parts = split(index5_value, "_")
    return length(parts) >= 1 ? parts[1] : "0"
end

"""
Extrait INDEX2 (nombre de cartes) depuis une valeur INDEX5
"""
function extraire_index2(index5_value::String)::String
    parts = split(index5_value, "_")
    return length(parts) >= 2 ? parts[2] : "A"
end

"""
Extrait INDEX3 (résultat) depuis une valeur INDEX5
"""
function extraire_index3(index5_value::String)::String
    parts = split(index5_value, "_")
    return length(parts) >= 3 ? parts[3] : "BANKER"
end

# ═══════════════════════════════════════════════════════════════════
# CALCULS D'ENTROPIE POUR INFORMATION MUTUELLE
# ═══════════════════════════════════════════════════════════════════

"""
Calcule l'entropie d'une distribution empirique
"""
function calculer_entropie_empirique(
    formulas::FormulasTheoretical{T},
    counts::Dict{String, Int},
    total::Int
) where T<:AbstractFloat
    if total <= 0
        return zero(T)
    end
    
    entropy = zero(T)
    for (value, count) in counts
        if count > 0
            p = T(count) / T(total)
            entropy -= p * (log(p) / log(formulas.base))
        end
    end
    return entropy
end

"""
Calcule l'entropie jointe empirique de deux variables
"""
function calculer_entropie_jointe_empirique(
    formulas::FormulasTheoretical{T},
    pairs::Vector{Tuple{String, String}}
) where T<:AbstractFloat
    if isempty(pairs)
        return zero(T)
    end
    
    # Compter les paires
    counts = Dict{Tuple{String, String}, Int}()
    for pair in pairs
        counts[pair] = get(counts, pair, 0) + 1
    end
    
    total = length(pairs)
    entropy = zero(T)
    
    for (pair, count) in counts
        if count > 0
            p = T(count) / T(total)
            entropy -= p * (log(p) / log(formulas.base))
        end
    end
    
    return entropy
end

# ═══════════════════════════════════════════════════════════════════
# FONCTION PRINCIPALE - Information Mutuelle Prédictive
# ═══════════════════════════════════════════════════════════════════

"""
    calculer_information_mutuelle_predictive(
        formulas::FormulasTheoretical{T}, 
        sequence::Vector{String}, 
        n::Int, 
        k::Int=5
    ) where T -> T

Calcule l'information mutuelle entre l'état actuel (INDEX1_n, INDEX2_n) 
et le résultat futur INDEX3_{n+1}, conditionnée par l'historique.

FORMULE : I(INDEX3_{n+1} ; INDEX1_n, INDEX2_n | Historique_{n-k:n})
        = H(INDEX3_{n+1} | Historique) - H(INDEX3_{n+1} | INDEX1_n, INDEX2_n, Historique)

INTERPRÉTATION PRÉDICTIVE :
- InfoMutT = 0 : INDEX1/INDEX2 n'apportent aucune information pour prédire INDEX3
- InfoMutT > 0 : INDEX1/INDEX2 contiennent de l'information prédictive
- Plus InfoMutT est élevé, plus INDEX1/INDEX2 sont utiles pour la prédiction

PARAMÈTRES :
- formulas : Structure contenant les probabilités théoriques
- sequence : Séquence complète INDEX5
- n : Position actuelle (on veut prédire INDEX3_{n+1})
- k : Taille de l'historique à considérer (défaut: 5)

RETOUR :
- Information mutuelle en bits (≥ 0)
"""
function calculer_information_mutuelle_predictive(
    formulas::FormulasTheoretical{T},
    sequence::Vector{String},
    n::Int,
    k::Int=5
) where T<:AbstractFloat
    
    # Vérifications de base
    if n <= k || n >= length(sequence)
        return zero(T)
    end
    
    # Extraire les données pour l'analyse
    historique_patterns = String[]
    index1_values = String[]
    index2_values = String[]
    index3_futurs = String[]
    
    # Parcourir la séquence pour construire les échantillons
    for i in (k+1):(n-1)  # On s'arrête à n-1 car on veut INDEX3_{i+1}
        # Historique des k dernières mains
        hist = join([sequence[j] for j in (i-k):i], "|")
        
        # État actuel
        idx1 = extraire_index1(sequence[i])
        idx2 = extraire_index2(sequence[i])
        
        # Résultat futur
        idx3_futur = extraire_index3(sequence[i+1])
        
        push!(historique_patterns, hist)
        push!(index1_values, idx1)
        push!(index2_values, idx2)
        push!(index3_futurs, idx3_futur)
    end
    
    if isempty(index3_futurs)
        return zero(T)
    end
    
    # CALCUL 1: H(INDEX3_{n+1} | Historique)
    # Grouper par historique et calculer l'entropie conditionnelle
    hist_groups = Dict{String, Vector{String}}()
    for (i, hist) in enumerate(historique_patterns)
        if !haskey(hist_groups, hist)
            hist_groups[hist] = String[]
        end
        push!(hist_groups[hist], index3_futurs[i])
    end
    
    h_index3_given_hist = zero(T)
    total_samples = length(index3_futurs)
    
    for (hist, idx3_list) in hist_groups
        weight = T(length(idx3_list)) / T(total_samples)
        
        # Entropie de INDEX3 pour cet historique spécifique
        counts = Dict{String, Int}()
        for idx3 in idx3_list
            counts[idx3] = get(counts, idx3, 0) + 1
        end
        
        h_local = calculer_entropie_empirique(formulas, counts, length(idx3_list))
        h_index3_given_hist += weight * h_local
    end
    
    # CALCUL 2: H(INDEX3_{n+1} | INDEX1_n, INDEX2_n, Historique)
    # Grouper par (historique, index1, index2) et calculer l'entropie conditionnelle
    combined_groups = Dict{Tuple{String, String, String}, Vector{String}}()
    for i in 1:length(historique_patterns)
        key = (historique_patterns[i], index1_values[i], index2_values[i])
        if !haskey(combined_groups, key)
            combined_groups[key] = String[]
        end
        push!(combined_groups[key], index3_futurs[i])
    end
    
    h_index3_given_all = zero(T)
    
    for (key, idx3_list) in combined_groups
        weight = T(length(idx3_list)) / T(total_samples)
        
        # Entropie de INDEX3 pour cette combinaison spécifique
        counts = Dict{String, Int}()
        for idx3 in idx3_list
            counts[idx3] = get(counts, idx3, 0) + 1
        end
        
        h_local = calculer_entropie_empirique(formulas, counts, length(idx3_list))
        h_index3_given_all += weight * h_local
    end
    
    # CALCUL FINAL: Information Mutuelle
    # I(INDEX3_{n+1} ; INDEX1_n, INDEX2_n | Historique) = H(INDEX3|Hist) - H(INDEX3|INDEX1,INDEX2,Hist)
    info_mutuelle = h_index3_given_hist - h_index3_given_all
    
    # S'assurer que le résultat est non-négatif (propriété mathématique)
    return max(zero(T), info_mutuelle)
end

# ═══════════════════════════════════════════════════════════════════
# EXPORTS
# ═══════════════════════════════════════════════════════════════════

export calculer_information_mutuelle_predictive

end # module InfoMutT
